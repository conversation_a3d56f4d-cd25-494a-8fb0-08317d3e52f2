@import './base.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* <PERSON><PERSON><PERSON><PERSON> lập cho dark mode để tr<PERSON>h chớp nh<PERSON>y khi tải trang */
html.dark-mode {
  background-color: #1a202c;
  color: #e2e8f0;
}

html.dark-mode body {
  background-color: #1a202c;
  color: #e2e8f0;
}

/* Transition mượt cho theme */
html, body {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Custom toast styling */
.custom-toast {
  border-radius: 8px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
  font-family: inherit !important;
  font-size: 14px !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s ease !important;
}

.custom-toast-body {
  font-weight: 500 !important;
  line-height: 1.4 !important;
}

/* Light theme toast styling */
.light-theme .Vue-Toastification__toast {
  background-color: #ffffff !important;
  color: #1f2937 !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
}

.light-theme .Vue-Toastification__toast--success {
  background-color: #f0fdf4 !important;
  color: #166534 !important;
  border-color: #22c55e !important;
}

.light-theme .Vue-Toastification__toast--error {
  background-color: #fef2f2 !important;
  color: #991b1b !important;
  border-color: #ef4444 !important;
}

.light-theme .Vue-Toastification__toast--warning {
  background-color: #fffbeb !important;
  color: #92400e !important;
  border-color: #f59e0b !important;
}

.light-theme .Vue-Toastification__toast--info {
  background-color: #eff6ff !important;
  color: #1e40af !important;
  border-color: #3b82f6 !important;
}

/* Dark mode support */
.dark-mode .Vue-Toastification__toast,
.dark-theme .Vue-Toastification__toast {
  background-color: #374151 !important;
  color: #f9fafb !important;
  border: 1px solid #4b5563 !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important;
}

.dark-mode .Vue-Toastification__toast--success,
.dark-theme .Vue-Toastification__toast--success {
  background-color: #065f46 !important;
  color: #d1fae5 !important;
  border-color: #10b981 !important;
}

.dark-mode .Vue-Toastification__toast--error,
.dark-theme .Vue-Toastification__toast--error {
  background-color: #7f1d1d !important;
  color: #fecaca !important;
  border-color: #ef4444 !important;
}

.dark-mode .Vue-Toastification__toast--warning,
.dark-theme .Vue-Toastification__toast--warning {
  background-color: #78350f !important;
  color: #fed7aa !important;
  border-color: #f59e0b !important;
}

.dark-mode .Vue-Toastification__toast--info,
.dark-theme .Vue-Toastification__toast--info {
  background-color: #1e3a8a !important;
  color: #dbeafe !important;
  border-color: #3b82f6 !important;
}

/* Progress bar styling */
.Vue-Toastification__progress-bar {
  height: 3px !important;
}

/* Light theme progress bars */
.light-theme .Vue-Toastification__toast--success .Vue-Toastification__progress-bar {
  background-color: #22c55e !important;
}

.light-theme .Vue-Toastification__toast--error .Vue-Toastification__progress-bar {
  background-color: #ef4444 !important;
}

.light-theme .Vue-Toastification__toast--warning .Vue-Toastification__progress-bar {
  background-color: #f59e0b !important;
}

.light-theme .Vue-Toastification__toast--info .Vue-Toastification__progress-bar {
  background-color: #3b82f6 !important;
}

/* Dark theme progress bars */
.dark-mode .Vue-Toastification__toast--success .Vue-Toastification__progress-bar,
.dark-theme .Vue-Toastification__toast--success .Vue-Toastification__progress-bar {
  background-color: #10b981 !important;
}

.dark-mode .Vue-Toastification__toast--error .Vue-Toastification__progress-bar,
.dark-theme .Vue-Toastification__toast--error .Vue-Toastification__progress-bar {
  background-color: #ef4444 !important;
}

.dark-mode .Vue-Toastification__toast--warning .Vue-Toastification__progress-bar,
.dark-theme .Vue-Toastification__toast--warning .Vue-Toastification__progress-bar {
  background-color: #f59e0b !important;
}

.dark-mode .Vue-Toastification__toast--info .Vue-Toastification__progress-bar,
.dark-theme .Vue-Toastification__toast--info .Vue-Toastification__progress-bar {
  background-color: #3b82f6 !important;
}

/* Animation improvements */
.Vue-Toastification__bounce-enter-active {
  animation-duration: 0.4s !important;
}

.Vue-Toastification__bounce-leave-active {
  animation-duration: 0.3s !important;
}

/* Close button styling */
.dark-mode .Vue-Toastification__close-button,
.dark-theme .Vue-Toastification__close-button {
  color: #d1d5db !important;
  opacity: 0.8 !important;
}

.dark-mode .Vue-Toastification__close-button:hover,
.dark-theme .Vue-Toastification__close-button:hover {
  opacity: 1 !important;
}

.light-theme .Vue-Toastification__close-button {
  color: #6b7280 !important;
  opacity: 0.8 !important;
}

.light-theme .Vue-Toastification__close-button:hover {
  opacity: 1 !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .Vue-Toastification__container {
    width: 100vw !important;
    padding: 0 16px !important;
  }

  .custom-toast {
    margin-bottom: 8px !important;
    border-radius: 6px !important;
  }
}
