@import './base.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* <PERSON><PERSON><PERSON><PERSON> lập cho dark mode để tr<PERSON>h chớp nh<PERSON>y khi tải trang */
html.dark-mode {
  background-color: #1a202c;
  color: #e2e8f0;
}

html.dark-mode body {
  background-color: #1a202c;
  color: #e2e8f0;
}

/* Transition mượt cho theme */
html, body {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Custom toast styling */
.custom-toast {
  border-radius: 8px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
  font-family: inherit !important;
  font-size: 14px !important;
  backdrop-filter: blur(10px) !important;
}

.custom-toast-body {
  font-weight: 500 !important;
  line-height: 1.4 !important;
}

/* Dark mode support */
.dark-mode .Vue-Toastification__toast {
  background-color: #374151 !important;
  color: #f9fafb !important;
  border: 1px solid #4b5563 !important;
}

.dark-mode .Vue-Toastification__toast--success {
  background-color: #065f46 !important;
  border-color: #10b981 !important;
}

.dark-mode .Vue-Toastification__toast--error {
  background-color: #7f1d1d !important;
  border-color: #ef4444 !important;
}

.dark-mode .Vue-Toastification__toast--warning {
  background-color: #78350f !important;
  border-color: #f59e0b !important;
}

.dark-mode .Vue-Toastification__toast--info {
  background-color: #1e3a8a !important;
  border-color: #3b82f6 !important;
}

/* Progress bar styling */
.Vue-Toastification__progress-bar {
  height: 3px !important;
}

/* Animation improvements */
.Vue-Toastification__bounce-enter-active {
  animation-duration: 0.4s !important;
}

.Vue-Toastification__bounce-leave-active {
  animation-duration: 0.3s !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .Vue-Toastification__container {
    width: 100vw !important;
    padding: 0 16px !important;
  }

  .custom-toast {
    margin-bottom: 8px !important;
    border-radius: 6px !important;
  }
}
