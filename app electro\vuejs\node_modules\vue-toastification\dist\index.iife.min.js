(()=>{var qt=Object.create;var L=Object.defineProperty;var Gt=Object.getOwnPropertyDescriptor;var Wt=Object.getOwnPropertyNames,at=Object.getOwnPropertySymbols,Xt=Object.getPrototypeOf,it=Object.prototype.hasOwnProperty,Kt=Object.prototype.propertyIsEnumerable;var ct=(t,e,s)=>e in t?L(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,M=(t,e)=>{for(var s in e||(e={}))it.call(e,s)&&ct(t,s,e[s]);if(at)for(var s of at(e))Kt.call(e,s)&&ct(t,s,e[s]);return t};var Jt=t=>L(t,"__esModule",{value:!0});var c=(t=>typeof require!="undefined"?require:typeof Proxy!="undefined"?new Proxy(t,{get:(e,s)=>(typeof require!="undefined"?require:e)[s]}):t)(function(t){if(typeof require!="undefined")return require.apply(this,arguments);throw new Error('Dynamic require of "'+t+'" is not supported')});var Yt=(t,e,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of Wt(e))!it.call(t,r)&&r!=="default"&&L(t,r,{get:()=>e[r],enumerable:!(s=Gt(e,r))||s.enumerable});return t},l=t=>Yt(Jt(L(t!=null?qt(Xt(t)):{},"default",t&&t.__esModule&&"default"in t?{get:()=>t.default,enumerable:!0}:{value:t,enumerable:!0})),t);var I=l(c("vue"));var H=l(c("vue"));var S=l(c("vue")),_=t=>typeof t=="function",N=t=>typeof t=="string",F=t=>N(t)&&t.trim().length>0,Qt=t=>typeof t=="number",y=t=>typeof t=="undefined",x=t=>typeof t=="object"&&t!==null,Zt=t=>f(t,"tag")&&F(t.tag),lt=t=>window.TouchEvent&&t instanceof TouchEvent,pt=t=>f(t,"component")&&z(t.component),te=t=>_(t)||x(t),z=t=>!y(t)&&(N(t)||te(t)||pt(t)),j=t=>x(t)&&["height","width","right","left","top","bottom"].every(e=>Qt(t[e])),f=(t,e)=>(x(t)||_(t))&&e in t,ut=(t=>()=>t++)(0);function U(t){return lt(t)?t.targetTouches[0].clientX:t.clientX}function q(t){return lt(t)?t.targetTouches[0].clientY:t.clientY}var mt=t=>{y(t.remove)?t.parentNode&&t.parentNode.removeChild(t):t.remove()},O=t=>pt(t)?O(t.component):Zt(t)?(0,S.defineComponent)({render(){return t}}):typeof t=="string"?t:(0,S.toRaw)((0,S.unref)(t)),dt=t=>{if(typeof t=="string")return t;let e=f(t,"props")&&x(t.props)?t.props:{},s=f(t,"listeners")&&x(t.listeners)?t.listeners:{};return{component:O(t),props:e,listeners:s}},ft=()=>typeof window!="undefined";var b=class{constructor(){this.allHandlers={}}getHandlers(e){return this.allHandlers[e]||[]}on(e,s){let r=this.getHandlers(e);r.push(s),this.allHandlers[e]=r}off(e,s){let r=this.getHandlers(e);r.splice(r.indexOf(s)>>>0,1)}emit(e,s){this.getHandlers(e).forEach(C=>C(s))}},vt=t=>["on","off","emit"].every(e=>f(t,e)&&_(t[e]));var $t=l(c("vue"));var u;(function(o){o.SUCCESS="success",o.ERROR="error",o.WARNING="warning",o.INFO="info",o.DEFAULT="default"})(u||(u={}));var B;(function(i){i.TOP_LEFT="top-left",i.TOP_CENTER="top-center",i.TOP_RIGHT="top-right",i.BOTTOM_LEFT="bottom-left",i.BOTTOM_CENTER="bottom-center",i.BOTTOM_RIGHT="bottom-right"})(B||(B={}));var m;(function(o){o.ADD="add",o.DISMISS="dismiss",o.UPDATE="update",o.CLEAR="clear",o.UPDATE_DEFAULTS="update_defaults"})(m||(m={}));var p="Vue-Toastification";var T={type:{type:String,default:u.DEFAULT},classNames:{type:[String,Array],default:()=>[]},trueBoolean:{type:Boolean,default:!0}},ht={type:T.type,customIcon:{type:[String,Boolean,Object,Function],default:!0}},$={component:{type:[String,Object,Function,Boolean],default:"button"},classNames:T.classNames,showOnHover:{type:Boolean,default:!1},ariaLabel:{type:String,default:"close"}},G={timeout:{type:[Number,Boolean],default:5e3},hideProgressBar:{type:Boolean,default:!1},isRunning:{type:Boolean,default:!1}},Tt={transition:{type:[Object,String],default:`${p}__bounce`}},ee={position:{type:String,default:B.TOP_RIGHT},draggable:T.trueBoolean,draggablePercent:{type:Number,default:.6},pauseOnFocusLoss:T.trueBoolean,pauseOnHover:T.trueBoolean,closeOnClick:T.trueBoolean,timeout:G.timeout,hideProgressBar:G.hideProgressBar,toastClassName:T.classNames,bodyClassName:T.classNames,icon:ht.customIcon,closeButton:$.component,closeButtonClassName:$.classNames,showCloseButtonOnHover:$.showOnHover,accessibility:{type:Object,default:()=>({toastRole:"alert",closeButtonLabel:"close"})},rtl:{type:Boolean,default:!1},eventBus:{type:Object,required:!1,default:()=>new b}},oe={id:{type:[String,Number],required:!0,default:0},type:T.type,content:{type:[String,Object,Function],required:!0,default:""},onClick:{type:Function,default:void 0},onClose:{type:Function,default:void 0}},ne={container:{type:[Object,Function],default:()=>document.body},newestOnTop:T.trueBoolean,maxToasts:{type:Number,default:20},transition:Tt.transition,toastDefaults:Object,filterBeforeCreate:{type:Function,default:t=>t},filterToasts:{type:Function,default:t=>t},containerClassName:T.classNames,onMounted:Function,shareAppContext:[Boolean,Object]},d={CORE_TOAST:ee,TOAST:oe,CONTAINER:ne,PROGRESS_BAR:G,ICON:ht,TRANSITION:Tt,CLOSE_BUTTON:$};var wt=l(c("vue"));var gt=l(c("vue"));var W=(0,gt.defineComponent)({name:"VtProgressBar",props:d.PROGRESS_BAR,data(){return{hasClass:!0}},computed:{style(){return{animationDuration:`${this.timeout}ms`,animationPlayState:this.isRunning?"running":"paused",opacity:this.hideProgressBar?0:1}},cpClass(){return this.hasClass?`${p}__progress-bar`:""}},watch:{timeout(){this.hasClass=!1,this.$nextTick(()=>this.hasClass=!0)}},mounted(){this.$el.addEventListener("animationend",this.animationEnded)},beforeUnmount(){this.$el.removeEventListener("animationend",this.animationEnded)},methods:{animationEnded(){this.$emit("close-toast")}}});var P=l(c("vue"));function Ct(t,e){return(0,P.openBlock)(),(0,P.createElementBlock)("div",{style:(0,P.normalizeStyle)(t.style),class:(0,P.normalizeClass)(t.cpClass)},null,6)}W.render=Ct;var yt=W;var Et=l(c("vue"));var X=(0,Et.defineComponent)({name:"VtCloseButton",props:d.CLOSE_BUTTON,computed:{buttonComponent(){return this.component!==!1?O(this.component):"button"},classes(){let t=[`${p}__close-button`];return this.showOnHover&&t.push("show-on-hover"),t.concat(this.classNames)}}});var h=l(c("vue")),se=(0,h.createTextVNode)(" \xD7 ");function Ot(t,e){return(0,h.openBlock)(),(0,h.createBlock)((0,h.resolveDynamicComponent)(t.buttonComponent),(0,h.mergeProps)({"aria-label":t.ariaLabel,class:t.classes},t.$attrs),{default:(0,h.withCtx)(()=>[se]),_:1},16,["aria-label","class"])}X.render=Ot;var Pt=X;var Vt=l(c("vue"));var K={};var V=l(c("vue")),re={"aria-hidden":"true",focusable:"false","data-prefix":"fas","data-icon":"check-circle",class:"svg-inline--fa fa-check-circle fa-w-16",role:"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},ae=(0,V.createElementVNode)("path",{fill:"currentColor",d:"M504 256c0 136.967-111.033 248-248 248S8 392.967 8 256 119.033 8 256 8s248 111.033 248 248zM227.314 387.314l184-184c6.248-6.248 6.248-16.379 0-22.627l-22.627-22.627c-6.248-6.249-16.379-6.249-22.628 0L216 308.118l-70.059-70.059c-6.248-6.248-16.379-6.248-22.628 0l-22.627 22.627c-6.248 6.248-6.248 16.379 0 22.627l104 104c6.249 6.249 16.379 6.249 22.628.001z"},null,-1),ie=[ae];function Dt(t,e){return(0,V.openBlock)(),(0,V.createElementBlock)("svg",re,ie)}K.render=Dt;var bt=K;var J={};var R=l(c("vue")),ce={"aria-hidden":"true",focusable:"false","data-prefix":"fas","data-icon":"info-circle",class:"svg-inline--fa fa-info-circle fa-w-16",role:"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},le=(0,R.createElementVNode)("path",{fill:"currentColor",d:"M256 8C119.043 8 8 119.083 8 256c0 136.997 111.043 248 248 248s248-111.003 248-248C504 119.083 392.957 8 256 8zm0 110c23.196 0 42 18.804 42 42s-18.804 42-42 42-42-18.804-42-42 18.804-42 42-42zm56 254c0 6.627-5.373 12-12 12h-88c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h12v-64h-12c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h64c6.627 0 12 5.373 12 12v100h12c6.627 0 12 5.373 12 12v24z"},null,-1),pe=[le];function Bt(t,e){return(0,R.openBlock)(),(0,R.createElementBlock)("svg",ce,pe)}J.render=Bt;var Y=J;var Q={};var k=l(c("vue")),ue={"aria-hidden":"true",focusable:"false","data-prefix":"fas","data-icon":"exclamation-circle",class:"svg-inline--fa fa-exclamation-circle fa-w-16",role:"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},me=(0,k.createElementVNode)("path",{fill:"currentColor",d:"M504 256c0 136.997-111.043 248-248 248S8 392.997 8 256C8 119.083 119.043 8 256 8s248 111.083 248 248zm-248 50c-25.405 0-46 20.595-46 46s20.595 46 46 46 46-20.595 46-46-20.595-46-46-46zm-43.673-165.346l7.418 136c.347 6.364 5.609 11.346 11.982 11.346h48.546c6.373 0 11.635-4.982 11.982-11.346l7.418-136c.375-6.874-5.098-12.654-11.982-12.654h-63.383c-6.884 0-12.356 5.78-11.981 12.654z"},null,-1),de=[me];function It(t,e){return(0,k.openBlock)(),(0,k.createElementBlock)("svg",ue,de)}Q.render=It;var St=Q;var Z={};var w=l(c("vue")),fe={"aria-hidden":"true",focusable:"false","data-prefix":"fas","data-icon":"exclamation-triangle",class:"svg-inline--fa fa-exclamation-triangle fa-w-18",role:"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 576 512"},ve=(0,w.createElementVNode)("path",{fill:"currentColor",d:"M569.517 440.013C587.975 472.007 564.806 512 527.94 512H48.054c-36.937 0-59.999-40.055-41.577-71.987L246.423 23.985c18.467-32.009 64.72-31.951 83.154 0l239.94 416.028zM288 354c-25.405 0-46 20.595-46 46s20.595 46 46 46 46-20.595 46-46-20.595-46-46-46zm-43.673-165.346l7.418 136c.347 6.364 5.609 11.346 11.982 11.346h48.546c6.373 0 11.635-4.982 11.982-11.346l7.418-136c.375-6.874-5.098-12.654-11.982-12.654h-63.383c-6.884 0-12.356 5.78-11.981 12.654z"},null,-1),he=[ve];function _t(t,e){return(0,w.openBlock)(),(0,w.createElementBlock)("svg",fe,he)}Z.render=_t;var Nt=Z;var tt=(0,Vt.defineComponent)({name:"VtIcon",props:d.ICON,computed:{customIconChildren(){return f(this.customIcon,"iconChildren")?this.trimValue(this.customIcon.iconChildren):""},customIconClass(){return N(this.customIcon)?this.trimValue(this.customIcon):f(this.customIcon,"iconClass")?this.trimValue(this.customIcon.iconClass):""},customIconTag(){return f(this.customIcon,"iconTag")?this.trimValue(this.customIcon.iconTag,"i"):"i"},hasCustomIcon(){return this.customIconClass.length>0},component(){return this.hasCustomIcon?this.customIconTag:z(this.customIcon)?O(this.customIcon):this.iconTypeComponent},iconTypeComponent(){return{[u.DEFAULT]:Y,[u.INFO]:Y,[u.SUCCESS]:bt,[u.ERROR]:Nt,[u.WARNING]:St}[this.type]},iconClasses(){let t=[`${p}__icon`];return this.hasCustomIcon?t.concat(this.customIconClass):t}},methods:{trimValue(t,e=""){return F(t)?t.trim():e}}});var v=l(c("vue"));function Rt(t,e){return(0,v.openBlock)(),(0,v.createBlock)((0,v.resolveDynamicComponent)(t.component),{class:(0,v.normalizeClass)(t.iconClasses)},{default:(0,v.withCtx)(()=>[(0,v.createTextVNode)((0,v.toDisplayString)(t.customIconChildren),1)]),_:1},8,["class"])}tt.render=Rt;var kt=tt;var et=(0,wt.defineComponent)({name:"VtToast",components:{ProgressBar:yt,CloseButton:Pt,Icon:kt},inheritAttrs:!1,props:Object.assign({},d.CORE_TOAST,d.TOAST),data(){return{isRunning:!0,disableTransitions:!1,beingDragged:!1,dragStart:0,dragPos:{x:0,y:0},dragRect:{}}},computed:{classes(){let t=[`${p}__toast`,`${p}__toast--${this.type}`,`${this.position}`].concat(this.toastClassName);return this.disableTransitions&&t.push("disable-transition"),this.rtl&&t.push(`${p}__toast--rtl`),t},bodyClasses(){return[`${p}__toast-${N(this.content)?"body":"component-body"}`].concat(this.bodyClassName)},draggableStyle(){return this.dragStart===this.dragPos.x?{}:this.beingDragged?{transform:`translateX(${this.dragDelta}px)`,opacity:1-Math.abs(this.dragDelta/this.removalDistance)}:{transition:"transform 0.2s, opacity 0.2s",transform:"translateX(0)",opacity:1}},dragDelta(){return this.beingDragged?this.dragPos.x-this.dragStart:0},removalDistance(){return j(this.dragRect)?(this.dragRect.right-this.dragRect.left)*this.draggablePercent:0}},mounted(){this.draggable&&this.draggableSetup(),this.pauseOnFocusLoss&&this.focusSetup()},beforeUnmount(){this.draggable&&this.draggableCleanup(),this.pauseOnFocusLoss&&this.focusCleanup()},methods:{hasProp:f,getVueComponentFromObj:O,closeToast(){this.eventBus.emit(m.DISMISS,this.id)},clickHandler(){this.onClick&&this.onClick(this.closeToast),this.closeOnClick&&(!this.beingDragged||this.dragStart===this.dragPos.x)&&this.closeToast()},timeoutHandler(){this.closeToast()},hoverPause(){this.pauseOnHover&&(this.isRunning=!1)},hoverPlay(){this.pauseOnHover&&(this.isRunning=!0)},focusPause(){this.isRunning=!1},focusPlay(){this.isRunning=!0},focusSetup(){addEventListener("blur",this.focusPause),addEventListener("focus",this.focusPlay)},focusCleanup(){removeEventListener("blur",this.focusPause),removeEventListener("focus",this.focusPlay)},draggableSetup(){let t=this.$el;t.addEventListener("touchstart",this.onDragStart,{passive:!0}),t.addEventListener("mousedown",this.onDragStart),addEventListener("touchmove",this.onDragMove,{passive:!1}),addEventListener("mousemove",this.onDragMove),addEventListener("touchend",this.onDragEnd),addEventListener("mouseup",this.onDragEnd)},draggableCleanup(){let t=this.$el;t.removeEventListener("touchstart",this.onDragStart),t.removeEventListener("mousedown",this.onDragStart),removeEventListener("touchmove",this.onDragMove),removeEventListener("mousemove",this.onDragMove),removeEventListener("touchend",this.onDragEnd),removeEventListener("mouseup",this.onDragEnd)},onDragStart(t){this.beingDragged=!0,this.dragPos={x:U(t),y:q(t)},this.dragStart=U(t),this.dragRect=this.$el.getBoundingClientRect()},onDragMove(t){this.beingDragged&&(t.preventDefault(),this.isRunning&&(this.isRunning=!1),this.dragPos={x:U(t),y:q(t)})},onDragEnd(){this.beingDragged&&(Math.abs(this.dragDelta)>=this.removalDistance?(this.disableTransitions=!0,this.$nextTick(()=>this.closeToast())):setTimeout(()=>{this.beingDragged=!1,j(this.dragRect)&&this.pauseOnHover&&this.dragRect.bottom>=this.dragPos.y&&this.dragPos.y>=this.dragRect.top&&this.dragRect.left<=this.dragPos.x&&this.dragPos.x<=this.dragRect.right?this.isRunning=!1:this.isRunning=!0}))}}});var n=l(c("vue")),Te=["role"];function xt(t,e){let s=(0,n.resolveComponent)("Icon"),r=(0,n.resolveComponent)("CloseButton"),C=(0,n.resolveComponent)("ProgressBar");return(0,n.openBlock)(),(0,n.createElementBlock)("div",{class:(0,n.normalizeClass)(t.classes),style:(0,n.normalizeStyle)(t.draggableStyle),onClick:e[0]||(e[0]=(...o)=>t.clickHandler&&t.clickHandler(...o)),onMouseenter:e[1]||(e[1]=(...o)=>t.hoverPause&&t.hoverPause(...o)),onMouseleave:e[2]||(e[2]=(...o)=>t.hoverPlay&&t.hoverPlay(...o))},[t.icon?((0,n.openBlock)(),(0,n.createBlock)(s,{key:0,"custom-icon":t.icon,type:t.type},null,8,["custom-icon","type"])):(0,n.createCommentVNode)("v-if",!0),(0,n.createElementVNode)("div",{role:t.accessibility.toastRole||"alert",class:(0,n.normalizeClass)(t.bodyClasses)},[typeof t.content=="string"?((0,n.openBlock)(),(0,n.createElementBlock)(n.Fragment,{key:0},[(0,n.createTextVNode)((0,n.toDisplayString)(t.content),1)],2112)):((0,n.openBlock)(),(0,n.createBlock)((0,n.resolveDynamicComponent)(t.getVueComponentFromObj(t.content)),(0,n.mergeProps)({key:1,"toast-id":t.id},t.hasProp(t.content,"props")?t.content.props:{},(0,n.toHandlers)(t.hasProp(t.content,"listeners")?t.content.listeners:{}),{onCloseToast:t.closeToast}),null,16,["toast-id","onCloseToast"]))],10,Te),t.closeButton?((0,n.openBlock)(),(0,n.createBlock)(r,{key:1,component:t.closeButton,"class-names":t.closeButtonClassName,"show-on-hover":t.showCloseButtonOnHover,"aria-label":t.accessibility.closeButtonLabel,onClick:(0,n.withModifiers)(t.closeToast,["stop"])},null,8,["component","class-names","show-on-hover","aria-label","onClick"])):(0,n.createCommentVNode)("v-if",!0),t.timeout?((0,n.openBlock)(),(0,n.createBlock)(C,{key:2,"is-running":t.isRunning,"hide-progress-bar":t.hideProgressBar,timeout:t.timeout,onCloseToast:t.timeoutHandler},null,8,["is-running","hide-progress-bar","timeout","onCloseToast"])):(0,n.createCommentVNode)("v-if",!0)],38)}et.render=xt;var At=et;var Lt=l(c("vue"));var ot=(0,Lt.defineComponent)({name:"VtTransition",props:d.TRANSITION,emits:["leave"],methods:{hasProp:f,leave(t){t instanceof HTMLElement&&(t.style.left=t.offsetLeft+"px",t.style.top=t.offsetTop+"px",t.style.width=getComputedStyle(t).width,t.style.position="absolute")}}});var g=l(c("vue"));function Mt(t,e){return(0,g.openBlock)(),(0,g.createBlock)(g.TransitionGroup,{tag:"div","enter-active-class":t.transition.enter?t.transition.enter:`${t.transition}-enter-active`,"move-class":t.transition.move?t.transition.move:`${t.transition}-move`,"leave-active-class":t.transition.leave?t.transition.leave:`${t.transition}-leave-active`,onLeave:t.leave},{default:(0,g.withCtx)(()=>[(0,g.renderSlot)(t.$slots,"default")]),_:3},8,["enter-active-class","move-class","leave-active-class","onLeave"])}ot.render=Mt;var Ut=ot;var nt=(0,$t.defineComponent)({name:"VueToastification",devtools:{hide:!0},components:{Toast:At,VtTransition:Ut},props:Object.assign({},d.CORE_TOAST,d.CONTAINER,d.TRANSITION),data(){return{count:0,positions:Object.values(B),toasts:{},defaults:{}}},computed:{toastArray(){return Object.values(this.toasts)},filteredToasts(){return this.defaults.filterToasts(this.toastArray)}},beforeMount(){let t=this.eventBus;t.on(m.ADD,this.addToast),t.on(m.CLEAR,this.clearToasts),t.on(m.DISMISS,this.dismissToast),t.on(m.UPDATE,this.updateToast),t.on(m.UPDATE_DEFAULTS,this.updateDefaults),this.defaults=this.$props},mounted(){this.setup(this.container)},methods:{async setup(t){_(t)&&(t=await t()),mt(this.$el),t.appendChild(this.$el)},setToast(t){y(t.id)||(this.toasts[t.id]=t)},addToast(t){t.content=dt(t.content);let e=Object.assign({},this.defaults,t.type&&this.defaults.toastDefaults&&this.defaults.toastDefaults[t.type],t),s=this.defaults.filterBeforeCreate(e,this.toastArray);s&&this.setToast(s)},dismissToast(t){let e=this.toasts[t];!y(e)&&!y(e.onClose)&&e.onClose(),delete this.toasts[t]},clearToasts(){Object.keys(this.toasts).forEach(t=>{this.dismissToast(t)})},getPositionToasts(t){let e=this.filteredToasts.filter(s=>s.position===t).slice(0,this.defaults.maxToasts);return this.defaults.newestOnTop?e.reverse():e},updateDefaults(t){y(t.container)||this.setup(t.container),this.defaults=Object.assign({},this.defaults,t)},updateToast({id:t,options:e,create:s}){this.toasts[t]?(e.timeout&&e.timeout===this.toasts[t].timeout&&e.timeout++,this.setToast(Object.assign({},this.toasts[t],e))):s&&this.addToast(Object.assign({},{id:t},e))},getClasses(t){return[`${p}__container`,t].concat(this.defaults.containerClassName)}}});var a=l(c("vue"));function Ht(t,e){let s=(0,a.resolveComponent)("Toast"),r=(0,a.resolveComponent)("VtTransition");return(0,a.openBlock)(),(0,a.createElementBlock)("div",null,[((0,a.openBlock)(!0),(0,a.createElementBlock)(a.Fragment,null,(0,a.renderList)(t.positions,C=>((0,a.openBlock)(),(0,a.createElementBlock)("div",{key:C},[(0,a.createVNode)(r,{transition:t.defaults.transition,class:(0,a.normalizeClass)(t.getClasses(C))},{default:(0,a.withCtx)(()=>[((0,a.openBlock)(!0),(0,a.createElementBlock)(a.Fragment,null,(0,a.renderList)(t.getPositionToasts(C),o=>((0,a.openBlock)(),(0,a.createBlock)(s,(0,a.mergeProps)({key:o.id},o),null,16))),128))]),_:2},1032,["transition","class"])]))),128))])}nt.render=Ht;var Ft=nt;var st=(t={},e=!0)=>{let s=t.eventBus=t.eventBus||new b;e&&(0,H.nextTick)(()=>{let o=(0,H.createApp)(Ft,M({},t)),i=o.mount(document.createElement("div")),D=t.onMounted;if(y(D)||D(i,o),t.shareAppContext){let E=t.shareAppContext;E===!0?console.warn(`[${p}] App to share context with was not provided.`):(o._context.components=E._context.components,o._context.directives=E._context.directives,o._context.mixins=E._context.mixins,o._context.provides=E._context.provides,o.config.globalProperties=E.config.globalProperties)}});let r=(o,i)=>{let D=Object.assign({},{id:ut(),type:u.DEFAULT},i,{content:o});return s.emit(m.ADD,D),D.id};r.clear=()=>s.emit(m.CLEAR,void 0),r.updateDefaults=o=>{s.emit(m.UPDATE_DEFAULTS,o)},r.dismiss=o=>{s.emit(m.DISMISS,o)};function C(o,{content:i,options:D},E=!1){let jt=Object.assign({},D,{content:i});s.emit(m.UPDATE,{id:o,options:jt,create:E})}return r.update=C,r.success=(o,i)=>r(o,Object.assign({},i,{type:u.SUCCESS})),r.info=(o,i)=>r(o,Object.assign({},i,{type:u.INFO})),r.error=(o,i)=>r(o,Object.assign({},i,{type:u.ERROR})),r.warning=(o,i)=>r(o,Object.assign({},i,{type:u.WARNING})),r};var ge=()=>{let t=()=>console.warn(`[${p}] This plugin does not support SSR!`);return new Proxy(t,{get(){return t}})};function A(t){return ft()?vt(t)?st({eventBus:t},!1):st(t,!0):ge()}var rt=Symbol("VueToastification"),zt=new b,Ce=(t,e)=>{(e==null?void 0:e.shareAppContext)===!0&&(e.shareAppContext=t);let s=A(M({eventBus:zt},e));t.provide(rt,s)},Bn=t=>{let e=A(t);(0,I.getCurrentInstance)()&&(0,I.provide)(rt,e)},In=t=>{if(t)return A(t);let e=(0,I.getCurrentInstance)()?(0,I.inject)(rt,void 0):void 0;return e||A(zt)},Sn=Ce;})();
