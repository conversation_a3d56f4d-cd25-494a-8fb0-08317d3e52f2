import { watch, onMounted, nextTick } from 'vue'
import { useThemeStore } from '../stores/theme'

export function useToastTheme() {
  const themeStore = useThemeStore()

  // Update toast container class based on theme
  const updateToastTheme = async () => {
    // Wait for DOM to be ready
    await nextTick()
    
    // Try multiple times to find the container (it might not exist yet)
    let attempts = 0
    const maxAttempts = 10
    
    const tryUpdate = () => {
      const toastContainer = document.querySelector('.Vue-Toastification__container')
      
      if (toastContainer) {
        if (themeStore.isDarkMode) {
          toastContainer.classList.add('dark-theme')
          toastContainer.classList.remove('light-theme')
        } else {
          toastContainer.classList.add('light-theme')
          toastContainer.classList.remove('dark-theme')
        }
        return true
      }
      
      attempts++
      if (attempts < maxAttempts) {
        setTimeout(tryUpdate, 100)
      }
      return false
    }
    
    tryUpdate()
  }

  // Watch for theme changes
  watch(() => themeStore.isDarkMode, () => {
    updateToastTheme()
  }, { immediate: false })

  // Update theme on mount
  onMounted(() => {
    // Delay to ensure toast container is created
    setTimeout(() => {
      updateToastTheme()
    }, 100)
  })

  // Force update theme (useful when toast container is created dynamically)
  const forceUpdateTheme = () => {
    updateToastTheme()
  }

  return {
    updateToastTheme,
    forceUpdateTheme
  }
}
