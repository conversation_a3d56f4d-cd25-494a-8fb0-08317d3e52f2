{"name": "v<PERSON><PERSON><PERSON>", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"pinia": "^3.0.3", "tesseract.js": "^6.0.1", "vue": "^3.5.13", "vue-i18n": "^12.0.0-alpha.2", "vue-router": "^4.5.0"}, "devDependencies": {"@playwright/test": "^1.51.1", "@tailwindcss/postcss7-compat": "^2.2.17", "@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^9.8.8", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "postcss": "^7.0.39", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}}