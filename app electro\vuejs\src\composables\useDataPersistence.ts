import { ref, computed } from 'vue'
import { useToast } from './useToast'
import { useI18n } from 'vue-i18n'

export interface CalculationRecord {
  id: string
  date: string
  timestamp: number
  electricityOld: number | string
  electricityNew: number | string
  electricityRate: number
  electricityUsage: number
  electricityTotal: number
  waterOld: number | string
  waterNew: number | string
  waterRate: number
  waterUsage: number
  waterTotal: number
  totalBill: number
}

export function useDataPersistence() {
  const { showSuccess, showError } = useToast()
  const { t } = useI18n()
  
  const records = ref<CalculationRecord[]>([])
  const isLoading = ref(false)
  const isSaving = ref(false)
  
  // File paths
  const DATA_FILE_NAME = 'utility_calculations.json'
  const BACKUP_FILE_NAME = 'utility_calculations_backup.json'
  
  // Load data from file
  const loadDataFromFile = async (): Promise<CalculationRecord[]> => {
    try {
      isLoading.value = true
      
      // Try to read from file using File System Access API (if supported)
      if ('showOpenFilePicker' in window) {
        const [fileHandle] = await (window as any).showOpenFilePicker({
          types: [{
            description: 'JSON files',
            accept: { 'application/json': ['.json'] }
          }],
          suggestedName: DATA_FILE_NAME
        })
        
        const file = await fileHandle.getFile()
        const content = await file.text()
        const data = JSON.parse(content)
        
        if (Array.isArray(data)) {
          records.value = data
          showSuccess(t('toast.success.dataLoaded', 'Dữ liệu đã được tải thành công!'))
          return data
        }
      }
      
      // Fallback to localStorage if File System Access API not supported
      const savedData = localStorage.getItem('utility_calculations')
      if (savedData) {
        const data = JSON.parse(savedData)
        records.value = data
        return data
      }
      
      return []
    } catch (error) {
      console.error('Error loading data:', error)
      showError(t('toast.error.dataLoadFailed', 'Không thể tải dữ liệu!'))
      return []
    } finally {
      isLoading.value = false
    }
  }
  
  // Save data to file
  const saveDataToFile = async (data: CalculationRecord[]): Promise<boolean> => {
    try {
      isSaving.value = true
      
      const jsonData = JSON.stringify(data, null, 2)
      
      // Try to save using File System Access API (if supported)
      if ('showSaveFilePicker' in window) {
        const fileHandle = await (window as any).showSaveFilePicker({
          types: [{
            description: 'JSON files',
            accept: { 'application/json': ['.json'] }
          }],
          suggestedName: DATA_FILE_NAME
        })
        
        const writable = await fileHandle.createWritable()
        await writable.write(jsonData)
        await writable.close()
        
        showSuccess(t('toast.success.dataSaved', 'Dữ liệu đã được lưu thành công!'))
        return true
      }
      
      // Fallback: Download as file
      const blob = new Blob([jsonData], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = DATA_FILE_NAME
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      // Also save to localStorage as backup
      localStorage.setItem('utility_calculations', jsonData)
      
      showSuccess(t('toast.success.dataSaved', 'Dữ liệu đã được lưu thành công!'))
      return true
    } catch (error) {
      console.error('Error saving data:', error)
      showError(t('toast.error.dataSaveFailed', 'Không thể lưu dữ liệu!'))
      return false
    } finally {
      isSaving.value = false
    }
  }
  
  // Add new calculation record
  const addRecord = async (record: Omit<CalculationRecord, 'id' | 'timestamp'>): Promise<boolean> => {
    const newRecord: CalculationRecord = {
      ...record,
      id: `calc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now()
    }
    
    records.value.unshift(newRecord) // Add to beginning
    return await saveDataToFile(records.value)
  }
  
  // Delete record
  const deleteRecord = async (id: string): Promise<boolean> => {
    const index = records.value.findIndex(r => r.id === id)
    if (index !== -1) {
      records.value.splice(index, 1)
      return await saveDataToFile(records.value)
    }
    return false
  }
  
  // Clear all records
  const clearAllRecords = async (): Promise<boolean> => {
    records.value = []
    return await saveDataToFile([])
  }
  
  // Export data
  const exportData = async (): Promise<void> => {
    await saveDataToFile(records.value)
  }
  
  // Import data
  const importData = async (): Promise<void> => {
    await loadDataFromFile()
  }
  
  // Computed properties
  const totalRecords = computed(() => records.value.length)
  const latestRecord = computed(() => records.value[0] || null)
  
  // Statistics
  const totalElectricitySpent = computed(() => 
    records.value.reduce((sum, record) => sum + Number(record.electricityTotal), 0)
  )
  
  const totalWaterSpent = computed(() => 
    records.value.reduce((sum, record) => sum + Number(record.waterTotal), 0)
  )
  
  const totalSpent = computed(() => 
    records.value.reduce((sum, record) => sum + Number(record.totalBill), 0)
  )
  
  return {
    // State
    records,
    isLoading,
    isSaving,
    
    // Methods
    loadDataFromFile,
    saveDataToFile,
    addRecord,
    deleteRecord,
    clearAllRecords,
    exportData,
    importData,
    
    // Computed
    totalRecords,
    latestRecord,
    totalElectricitySpent,
    totalWaterSpent,
    totalSpent
  }
}
