import { ref, computed } from 'vue'
import { useElectricityCalculator } from './useElectricityCalculator'
import { useWaterCalculator } from './useWaterCalculator'
import { useFormatterUtils } from './useFormatterUtils'
import { useTabControl } from './useTabControl'
import { useImageOCR } from './useImageOCR'
import { useFireworks } from './useFireworks'
import { useI18n } from 'vue-i18n'

export function useUtilityCalculator() {
  const { formatCurrency } = useFormatterUtils()
  const { t } = useI18n()
  
  // Get electricity and water calculators
  const electricity = useElectricityCalculator()
  const water = useWaterCalculator()

  // Get OCR functionality
  const { isProcessing, progress, processImage } = useImageOCR()

  // Get tab control
  const { activeTab, changeTab } = useTabControl('both')

  // Get fireworks
  const { showFireworks, triggerFireworks } = useFireworks()

  // Computed total bill
  const totalBill = computed(() => {
    return electricity.electricityTotal.value + water.waterTotal.value
  })

  // Methods
  const resetForm = () => {
    electricity.resetElectricity()
    water.resetWater()
    console.log('🔄 Đã reset form thành công!')
  }

  const fillSampleData = () => {
    electricity.fillElectricitySampleData()
    water.fillWaterSampleData()
    console.log('📝 Đã điền dữ liệu mẫu!')
  }

  // Process image and set value
  const processImageAndSetValue = async (file: File, type: 'electricity' | 'water', isOld: boolean) => {
    if (!file) return
    
    try {
      const result = await processImage(file)
      
      if (result) {
        if (type === 'electricity') {
          electricity.setElectricityValueFromOCR(result, isOld)
        } else {
          water.setWaterValueFromOCR(result, isOld)
        }
      }
    } catch (error) {
      console.error('Error processing image:', error)
      console.error('❌ Lỗi khi xử lý ảnh!')

      // Hiển thị hộp thoại nhập thủ công khi OCR thất bại
      promptManualEntry(type, isOld)
    }
  }
  
  // Hiển thị hộp thoại nhập thủ công khi OCR thất bại
  const promptManualEntry = (type: 'electricity' | 'water', isOld: boolean) => {
    // Hiển thị thông báo gợi ý
    console.info('🔢 ' + t('calculator.imageUpload.manualEntry'))

    // Focus vào input tương ứng
    setTimeout(() => {
      if (type === 'electricity') {
        const inputId = isOld ? 'electricity-old' : 'electricity-new'
        const inputElement = document.getElementById(inputId) as HTMLInputElement
        if (inputElement) {
          inputElement.focus()
        }
      } else {
        const inputId = isOld ? 'water-old' : 'water-new'
        const inputElement = document.getElementById(inputId) as HTMLInputElement
        if (inputElement) {
          inputElement.focus()
        }
      }
    }, 500)
  }

  // Trigger fireworks when viewing details
  const viewDetails = () => {
    // Hiện pháo hoa khi xem chi tiết
    triggerFireworks(3000)
    return true
  }

  return {
    // State from electricity calculator
    electricityOld: electricity.electricityOld,
    electricityNew: electricity.electricityNew,
    electricityRate: electricity.electricityRate,
    defaultElectricityRate: electricity.defaultElectricityRate,
    
    // Computed from electricity calculator
    electricityUsage: electricity.electricityUsage,
    electricityTotal: electricity.electricityTotal,
    
    // Methods from electricity calculator
    validateElectricityReadings: electricity.validateElectricityReadings,
    
    // State from water calculator
    waterOld: water.waterOld,
    waterNew: water.waterNew,
    waterRate: water.waterRate,
    defaultWaterRate: water.defaultWaterRate,
    
    // Computed from water calculator
    waterUsage: water.waterUsage,
    waterTotal: water.waterTotal,
    
    // Methods from water calculator
    validateWaterReadings: water.validateWaterReadings,
    
    // Tab control
    activeTab,
    changeTab,
    
    // Image OCR
    isProcessing,
    progress,
    processImageAndSetValue,
    
    // Fireworks
    showFireworks,
    
    // Utility methods
    totalBill,
    resetForm,
    fillSampleData,
    formatCurrency,
    viewDetails
  }
} 